<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">FloatingButton 微信小程序测试</text>
    </view>

    <view class="test-content">
      <view class="test-section">
        <text class="section-title">测试日志</text>
        <view class="log-container">
          <view class="log-item" v-for="(log, index) in logs" :key="index">
            <text class="log-text">{{ log }}</text>
          </view>
        </view>
      </view>

      <view class="test-section">
        <text class="section-title">操作说明</text>
        <text class="instruction">1. 点击悬浮按钮测试点击事件</text>
        <text class="instruction">2. 拖拽悬浮按钮测试拖拽功能</text>
        <text class="instruction">3. 查看控制台和日志输出</text>
      </view>

      <view class="test-section">
        <text class="section-title">状态信息</text>
        <text class="status-text">点击次数: {{ clickCount }}</text>
        <text class="status-text">拖拽次数: {{ dragCount }}</text>
        <text class="status-text">当前位置: x={{ currentPosition.x }}, y={{ currentPosition.y }}</text>
      </view>
    </view>

    <!-- 测试用的悬浮按钮 -->
    <FloatingButton
      :draggable="true"
      :size="80"
      icon="move"
      background-color="#33aa71"
      :initial-position="{ x: 50, y: 300 }"
      :bounds="{ top: 100, right: 50, bottom: 100, left: 50 }"
      @click="handleFloatingClick"
      @position-change="handlePositionChange"
      @drag-start="handleDragStart"
      @drag-end="handleDragEnd"
    >
      <wd-icon name="chat" size="24px" color="#ffffff"></wd-icon>
    </FloatingButton>

    <!-- 固定位置测试按钮 -->
    <FloatingButton :draggable="false" :size="60" icon="add" background-color="#ff6b6b" position="left-bottom" :offset="{ x: 32, y: 120 }" @click="handleFixedClick">
      <wd-icon name="add" size="20px" color="#ffffff"></wd-icon>
    </FloatingButton>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FloatingButton from '@/components/FloatingButton/index.vue'

const logs = ref([])
const clickCount = ref(0)
const dragCount = ref(0)
const currentPosition = ref({ x: 50, y: 300 })

function addLog(message) {
  const timestamp = new Date().toLocaleTimeString()
  const logMessage = `[${timestamp}] ${message}`
  logs.value.unshift(logMessage)
  console.log(logMessage)

  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

function handleFloatingClick() {
  clickCount.value++
  addLog(`拖拽悬浮按钮被点击 (第${clickCount.value}次)`)

  // 显示提示
  uni.showToast({
    title: `拖拽按钮点击 ${clickCount.value}`,
    icon: 'success',
    duration: 1000
  })
}

function handleFixedClick() {
  addLog(`固定位置按钮被点击`)

  // 显示提示
  uni.showToast({
    title: '固定按钮点击',
    icon: 'success',
    duration: 1000
  })
}

function handlePositionChange(position) {
  currentPosition.value = position
  addLog(`位置变化: x=${position.x}, y=${position.y}`)
}

function handleDragStart() {
  addLog('开始拖拽')
}

function handleDragEnd() {
  dragCount.value++
  addLog(`结束拖拽 (第${dragCount.value}次)`)
}

// 页面加载时添加初始日志
addLog('页面加载完成，开始测试')
</script>

<style lang="less" scoped>
.test-container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-content {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.test-section {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.log-container {
  max-height: 400rpx;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 12rpx;
  padding: 12rpx;
  background: #fff;
  border-radius: 8rpx;
  border-left: 4rpx solid #33aa71;

  &:last-child {
    margin-bottom: 0;
  }
}

.log-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.instruction {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
  line-height: 1.5;
}

.status-text {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
}
</style>
