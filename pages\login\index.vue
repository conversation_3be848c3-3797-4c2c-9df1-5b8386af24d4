<template>
  <div class="wrap all back-white">
    <!-- 增强背景装饰 -->
    <div class="bg-enhancements">
      <!-- 浮动圆圈 -->
      <div class="floating-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="circle circle-4"></div>
      </div>

      <!-- 光线效果 -->
      <div class="light-rays">
        <div class="ray ray-1"></div>
        <div class="ray ray-2"></div>
        <div class="ray ray-3"></div>
      </div>

      <!-- 渐变遮罩 -->
      <div class="gradient-overlay"></div>
    </div>

    <!-- 标题区域 -->
    <div class="title-section">
      <div class="title">账号密码快捷登录</div>
      <div class="title_for">忘记密码请联系管理员</div>
    </div>

    <!-- 登录表单区域 -->
    <div class="plate">
      <div class="input-box">
        <div class="fon-S36 fon-W600">账号</div>
        <div class="input-box-right">
          <wd-input type="text" :focus-when-clear="false" v-model="formData.username" clearable placeholder="请输入账号" />
        </div>
      </div>
      <div class="input-box">
        <div class="fon-S36 fon-W600">密码</div>
        <div class="input-box-right">
          <wd-input v-model="formData.password" clearable show-password placeholder="请输入密码" />
        </div>
      </div>
    </div>

    <!-- 登录按钮 -->
    <div class="btn" @click="handleLogin" :class="{ 'btn-disabled': isAllow }">
      <span class="btn-text">登录</span>
      <div class="btn-ripple"></div>
    </div>

    <!-- 其他登录方式 -->
    <div class="bot f-xy-center">
      <div class="divider-line"></div>
      <div class="fon-S24 pad-X12 color-333">其他登录方式</div>
      <div class="divider-line"></div>
    </div>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'

import { login } from '@/services/model/login'
import { cache } from '@/utils/cache'
import { useToast } from 'wot-design-uni'

const toast = useToast()

const formData = reactive({ username: null, password: null })
const isAllow = computed(() => !(formData.username && formData.password))
const isPastDue = ref(false)
const redirect = ref(false)
onLoad((e) => {
  isPastDue.value = cache.get('token') ? false : true
})

// 登录
async function handleLogin() {
  if (isAllow.value) {
    toast.error('请输入完整信息')
    return
  }

  try {
    const { code, token, data } = await login(formData)

    if (code === 200) {
      cache.set('token', token)
      cache.set('userInfo', JSON.parse(data)[0])
      toast.success('登录成功')
      setTimeout(() => {
        let stepNumber = 0
        let lastPage = null
        const pages = getCurrentPages().reverse()
        for (let i = 0; i < pages.length; i++) {
          const page = pages[i]
          if (page.route === 'pages/login/index') {
            stepNumber++
          } else {
            lastPage = page.route
            // 结束循环
            break
          }
        }
        console.log(stepNumber, pages, lastPage)

        if (isPastDue.value || lastPage === 'pages/zone-record/detail') {
          uni.navigateBack({ delta: stepNumber })
        } else {
          uni.reLaunch({ url: '/pages/home/<USER>' })
        }
      }, 1000)
    }
  } catch (error) {
    toast.error('登录失败，请检查用户名和密码是否正确')
  }
}
</script>

<style lang="less" scoped>
.wrap {
  background-image: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9)), url('/static/img/bg2.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
  padding: 300rpx 90rpx 0 90rpx;

  // 增强背景装饰
  .bg-enhancements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;

    // 简化的浮动圆圈
    .floating-circles {
      position: absolute;
      width: 100%;
      height: 100%;

      .circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(59, 130, 246, 0.03);
        border: 1rpx solid rgba(59, 130, 246, 0.08);

        &.circle-1 {
          width: 120rpx;
          height: 120rpx;
          top: 15%;
          right: -30rpx;
          animation: floatSlow 12s ease-in-out infinite;
        }

        &.circle-2 {
          width: 80rpx;
          height: 80rpx;
          bottom: 25%;
          left: -20rpx;
          animation: floatSlow 15s ease-in-out infinite reverse;
        }

        &.circle-3 {
          width: 60rpx;
          height: 60rpx;
          top: 45%;
          left: 15%;
          animation: floatSlow 10s ease-in-out infinite;
        }

        &.circle-4 {
          width: 40rpx;
          height: 40rpx;
          bottom: 35%;
          right: 20%;
          animation: floatSlow 18s ease-in-out infinite reverse;
        }
      }
    }

    // 简化的光线效果
    .light-rays {
      position: absolute;
      width: 100%;
      height: 100%;

      .ray {
        position: absolute;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
        border-radius: 1rpx;

        &.ray-1 {
          width: 200rpx;
          height: 2rpx;
          top: 30%;
          left: -100rpx;
          animation: rayMove 20s linear infinite;
        }

        &.ray-2 {
          width: 150rpx;
          height: 1rpx;
          bottom: 40%;
          right: -75rpx;
          animation: rayMove 25s linear infinite reverse;
        }

        &.ray-3 {
          width: 180rpx;
          height: 1rpx;
          top: 60%;
          left: -90rpx;
          animation: rayMove 30s linear infinite;
        }
      }
    }

    // 简化的渐变遮罩
    .gradient-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
      animation: gradientShift 30s ease-in-out infinite;
    }
  }

  // 标题区域
  .title-section {
    position: relative;
    z-index: 10;
    text-align: center;
    margin-bottom: 80rpx;

    .title {
      font-size: 44rpx;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 16rpx;
      animation: fadeInUp 0.8s ease-out both;
    }

    .title_for {
      font-size: 26rpx;
      color: #6b7280;
      font-weight: 400;
      animation: fadeInUp 1s ease-out 0.2s both;
    }
  }

  // 表单区域
  .plate {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: 16rpx;
    padding: 48rpx 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid rgba(0, 0, 0, 0.06);
    margin-bottom: 40rpx;
    animation: slideInUp 0.8s ease-out 0.3s both;

    .input-box {
      margin-bottom: 32rpx;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .fon-S36 {
        margin-bottom: 12rpx;
        color: #1f2937;
        font-weight: 500;
        font-size: 30rpx;
        position: relative;
      }

      .input-box-right {
        :deep(.wd-input) {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
          background: #ffffff;
          border: 1rpx solid #e5e7eb;
          border-radius: 8rpx;
          padding: 0 16rpx;
          font-size: 30rpx;
          transition: all 0.2s ease;
          box-shadow: none;
          min-height: 88rpx;
          height: 88rpx;
          line-height: 1.5;
          color: #374151;

          &:focus-within {
            border-color: #3b82f6;
            background: #ffffff;
            box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.1);
            outline: none;
          }

          &::placeholder {
            color: #9ca3af;
            font-size: 30rpx;
          }
        }

        :deep(.wd-input__icon),
        :deep(.wd-input__clear) {
          background: transparent;
          color: #6b7280;
        }

        :deep(.wd-input__suffix) {
          color: #3b82f6;
        }
      }
    }
  }

  // 登录按钮
  .btn {
    position: relative;
    width: 100%;
    background: #3b82f6;
    border-radius: 8rpx;
    padding: 0;
    text-align: center;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1), 0 1rpx 2rpx rgba(0, 0, 0, 0.06);
    margin-bottom: 40rpx;
    animation: slideInUp 1s ease-out 0.6s both;

    &:active {
      transform: translateY(1rpx);
      box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
    }

    &:hover {
      background: #2563eb;
      box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1), 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
    }

    &.btn-disabled {
      background: #9ca3af;
      box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
      cursor: not-allowed;

      &:active {
        transform: none;
      }

      &:hover {
        background: #9ca3af;
        box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
      }

      .btn-ripple {
        display: none;
      }
    }

    .btn-text {
      position: relative;
      z-index: 2;
      font-size: 32rpx;
      font-weight: 500;
      color: #ffffff;
      letter-spacing: 0.5rpx;
      padding: 24rpx;
      display: block;
    }

    // 简化的波纹效果
    .btn-ripple {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
      z-index: 1;
      animation: rippleMove 4s linear infinite;
    }
  }

  // 底部区域
  .bot {
    position: relative;
    z-index: 10;
    margin-top: 80rpx;
    animation: fadeInUp 1.2s ease-out 0.9s both;

    .divider-line {
      width: 20%;
      height: 1rpx;
      background: #e5e7eb;
    }

    .fon-S24 {
      color: #9ca3af;
      font-weight: 400;
      font-size: 26rpx;
    }
  }

  // 动画定义
  @keyframes floatSlow {
    0%,
    100% {
      transform: translateY(0) rotate(0deg);
    }
    50% {
      transform: translateY(-30rpx) rotate(180deg);
    }
  }

  @keyframes rayMove {
    0% {
      transform: translateX(-100%) rotate(15deg);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(100vw) rotate(15deg);
      opacity: 0;
    }
  }

  @keyframes gradientShift {
    0%,
    100% {
      transform: scale(1) rotate(0deg);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.1) rotate(180deg);
      opacity: 1;
    }
  }

  @keyframes titleGlow {
    0% {
      filter: drop-shadow(0 0 10rpx rgba(77, 128, 240, 0.3));
    }
    100% {
      filter: drop-shadow(0 0 20rpx rgba(77, 128, 240, 0.6));
    }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(40rpx);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInUp {
    0% {
      opacity: 0;
      transform: translateY(60rpx) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes rippleMove {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes buttonGlow {
    0%,
    100% {
      box-shadow: 0 0 20rpx rgba(77, 128, 240, 0.4);
    }
    50% {
      box-shadow: 0 0 40rpx rgba(77, 128, 240, 0.8);
    }
  }

  @keyframes lineGlow {
    0%,
    100% {
      opacity: 0.3;
      transform: scaleX(0.5);
    }
    50% {
      opacity: 1;
      transform: scaleX(1);
    }
  }

  @keyframes dotPulse {
    0%,
    100% {
      opacity: 0.3;
      transform: translateY(-50%) scale(1);
    }
    50% {
      opacity: 1;
      transform: translateY(-50%) scale(1.2);
    }
  }
}
</style>
